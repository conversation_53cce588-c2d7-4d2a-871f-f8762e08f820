# FastAPI Backend for MedCode AI - Separate Backend Only
import requests
from bs4 import BeautifulSoup
import google.generativeai as genai
import hashlib
import psycopg2
import os
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
import io
import base64
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image as ReportLabImage
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER
from html2text import html2text
import PyPDF2
from fastapi import FastAPI, HTTPException, File, UploadFile, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, Response
from pydantic import BaseModel
from typing import Optional, List
import uvicorn
import asyncio
from contextlib import asynccontextmanager

# Load environment variables
load_dotenv()

# Configure Google Generative AI
# Try to import from local file first, then fall back to environment variable
try:
    import sys
    sys.path.append('./MedX-main')
    from google_api_key import google_api_key
except ImportError:
    google_api_key = os.getenv("GOOGLE_API_KEY", "AIzaSyDzvhjPEiiIVe191_X2qi5Kdxuk3T77g7c")

genai.configure(api_key=google_api_key)

# Lifespan event handler
@asynccontextmanager
async def lifespan(_: FastAPI):
    # Startup
    try:
        init_db()
        print("Database initialized successfully")
    except Exception as e:
        print(f"Database initialization failed: {e}")
        print("API will continue without database features")
    yield
    # Shutdown (if needed)

# FastAPI app initialization
app = FastAPI(
    title="MedCode AI API",
    description="API for MedCode AI medical assistant",
    version="1.0.0",
    lifespan=lifespan
)

# Allow CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# PostgreSQL Database Configuration
def get_db_connection():
    """
    Create a secure PostgreSQL connection using environment variables from .env
    """
    try:
        load_dotenv()
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            # Try alternative environment variables for local development
            db_host = os.getenv("DB_HOST", "localhost")
            db_name = os.getenv("DB_NAME", "medcodeai")
            db_user = os.getenv("DB_USER", "postgres")
            db_password = os.getenv("DB_PASSWORD", "")
            db_port = os.getenv("DB_PORT", "5432")

            if db_password:
                database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
            else:
                print("Warning: No database configuration found. Database features will be disabled.")
                return None

        if "render.com" in database_url:
            database_url += "?sslmode=require"
        conn = psycopg2.connect(database_url)
        return conn
    except Exception as e:
        print(f"Database connection error: {str(e)}")
        print("Database features will be disabled. The API will still work for non-database operations.")
        return None

# Pydantic models for request/response
class UserLogin(BaseModel):
    username: str
    password: str

class UserSignup(BaseModel):
    username: str
    password: str
    name: str
    email: str
    role: str

class QueryRequest(BaseModel):
    user_id: int
    query: str
    role: str
    language: Optional[str] = "English"
    has_image: Optional[bool] = False

class QueryWithFileRequest(BaseModel):
    user_id: int
    query: str
    role: str
    language: Optional[str] = "English"

class QueryHistoryResponse(BaseModel):
    id: int
    query: str
    role: str
    timestamp: str
    has_image: bool

class PubMedRequest(BaseModel):
    query: str
    num_articles: Optional[int] = 5

class ProfileUpdateRequest(BaseModel):
    user_id: int
    name: Optional[str] = None
    email: Optional[str] = None

class PasswordChangeRequest(BaseModel):
    user_id: int
    current_password: str
    new_password: str

# Model Configuration
generation_config = {
    "temperature": 0.2,
    "top_p": 0.9,
    "top_k": 40,
    "max_output_tokens": 8192,
}

safety_settings = [
    {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
]

# Initialize model
model = genai.GenerativeModel(model_name="gemini-2.0-flash",
                             generation_config=generation_config,
                             safety_settings=safety_settings)

# Role-Specific Prompts
role_prompts = {
    "Student": """
    You are a highly skilled AI assistant designed to help medical students.
    Your task is to provide in-depth explanations on medical conditions, treatment procedures, 
    and related case studies. Also, retrieve the latest research papers and medical findings.
    Provide links to research papers when applicable.
    
    Please structure the response as follows:
    1. *Detailed Explanation*  
    2. *Case Studies*  
    3. *Research Paper References* (Fetched from PubMed)
    """,
    "Doctor": """
    You are a highly skilled AI assistant designed to help doctors and medical professionals.
    Your task is to provide detailed medical information, differential diagnoses, treatment protocols,
    and the latest clinical guidelines. Focus on evidence-based medicine and current best practices.
    
    Please structure the response as follows:
    1. *Clinical Assessment*
    2. *Differential Diagnosis*
    3. *Treatment Recommendations*
    4. *Latest Clinical Guidelines*
    """,
    "Common User": """
    You are a helpful AI assistant designed to provide general medical information to the public.
    Your task is to explain medical conditions and treatments in simple, easy-to-understand language.
    Always emphasize the importance of consulting healthcare professionals for proper diagnosis and treatment.
    
    Please structure the response as follows:
    1. *Simple Explanation*
    2. *When to See a Doctor*
    3. *General Health Tips*
    """
}

# API Endpoints

@app.post("/login")
def api_login(user: UserLogin):
    """User login endpoint"""
    try:
        authenticated, role, name, user_id = verify_credentials(user.username, user.password)
        if not authenticated:
            raise HTTPException(status_code=401, detail="Invalid credentials")
        return {"authenticated": True, "role": role, "name": name, "user_id": user_id}
    except Exception as e:
        if "Database connection error" in str(e):
            raise HTTPException(status_code=503, detail="Database service unavailable")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/signup")
def api_signup(user: UserSignup):
    """User signup endpoint"""
    success, message = create_account(user.username, user.password, user.name, user.email, user.role)
    if not success:
        raise HTTPException(status_code=400, detail=message)
    return {"success": True, "message": message}

@app.post("/query")
def api_query(req: QueryRequest):
    """Query medical analysis (text only)"""
    # Get role-based prompt
    role_prompt = role_prompts.get(req.role, "")

    # Add strong language instruction if not English
    if req.language and req.language != "English":
        language_instruction = f"""

CRITICAL LANGUAGE REQUIREMENT:
- You MUST respond entirely in {req.language} language
- Do NOT use any English words in your response
- Translate all medical terms to {req.language}
- If you don't know a medical term in {req.language}, provide the closest translation and explanation
- The user specifically requested a response in {req.language}, so this is mandatory
        """
        role_prompt += language_instruction

    prompt_parts = [req.query, role_prompt]
    response = model.generate_content(prompt_parts)
    if not response:
        raise HTTPException(status_code=500, detail="AI model failed to generate response")
    save_query_history(req.user_id, req.query, req.role, req.has_image)
    return {"response": response.text}

# File upload endpoint - requires python-multipart to be installed
# Install first: pip install python-multipart

@app.post("/query-with-file")
async def api_query_with_file(
    user_id: int = Form(...),
    query: str = Form(...),
    role: str = Form(...),
    language: str = Form("English"),
    file: Optional[UploadFile] = File(None)
):
    # Query medical analysis with optional file upload (image or PDF)
    prompt_parts = []
    has_image = False

    # Handle file upload
    if file:
        if file.content_type.startswith('image'):
            # Handle image upload
            image_data = await file.read()
            image_parts = [{"mime_type": file.content_type, "data": image_data}]
            prompt_parts.append(image_parts[0])
            has_image = True
        elif file.content_type == 'application/pdf':
            # Handle PDF upload
            pdf_content = await file.read()
            pdf_text = extract_text_from_pdf_bytes(pdf_content)
            prompt_parts.append(f"PDF CONTENT:\n{pdf_text}\n\nUSER QUERY:")

    # Add user query and role-based prompt
    prompt_parts.append(query)

    # Add language instruction if not English
    role_prompt = role_prompts.get(role, "")
    if language != "English":
        role_prompt += f"\n\nPlease provide your response in {language}."

    prompt_parts.append(role_prompt)

    # Generate response
    try:
        response = model.generate_content(prompt_parts)
        if not response:
            raise HTTPException(status_code=500, detail="AI model failed to generate response")

        # Save query to history
        save_query_history(user_id, query, role, has_image)

        return {"response": response.text}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating response: {str(e)}")

@app.get("/history/{user_id}")
def api_history(user_id: int, limit: int = 20):
    """Get user query history"""
    try:
        history = get_user_query_history(user_id, limit)
        # Convert datetime objects to strings if needed
        formatted_history = []
        for record in history:
            formatted_record = dict(record)
            if 'timestamp' in formatted_record and formatted_record['timestamp']:
                formatted_record['timestamp'] = str(formatted_record['timestamp'])
            formatted_history.append(formatted_record)
        return {"history": formatted_history, "count": len(formatted_history)}
    except Exception as e:
        print(f"Error in api_history: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving history: {str(e)}")

@app.post("/pubmed")
async def api_pubmed(req: PubMedRequest):
    """Fetch medical research papers from PubMed"""
    try:
        articles = await fetch_medical_articles_async(req.query, req.num_articles)
        return {"results": articles}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/profile/update")
def api_update_profile(req: ProfileUpdateRequest):
    """Update user profile"""
    success, message = update_profile(req.user_id, req.name, req.email)
    if not success:
        raise HTTPException(status_code=400, detail=message)
    return {"success": True, "message": message}

@app.post("/profile/change-password")
def api_change_password(req: PasswordChangeRequest):
    """Change user password"""
    success, message = change_password(req.user_id, req.current_password, req.new_password)
    if not success:
        raise HTTPException(status_code=400, detail=message)
    return {"success": True, "message": message}

@app.get("/stats/{user_id}")
def api_user_stats(user_id: int):
    """Get user statistics"""
    stats = get_user_stats(user_id)
    return stats

# PDF generation endpoint - requires python-multipart to be installed
# Install first: pip install python-multipart

@app.post("/generate-pdf")
async def api_generate_pdf(
    user_name: str = Form(...),
    role: str = Form(...),
    query: str = Form(...),
    response_text: str = Form(...),
    language: str = Form("English"),
    file: Optional[UploadFile] = File(default=None)
):
    # Generate PDF report
    try:
        image_data = None
        pdf_content = None

        if file and hasattr(file, 'content_type') and file.content_type:
            if file.content_type.startswith('image'):
                image_data = await file.read()
            elif file.content_type == 'application/pdf':
                pdf_bytes = await file.read()
                pdf_content = extract_text_from_pdf_bytes(pdf_bytes)

        pdf_bytes = create_medical_pdf(
            user_name, role, query, response_text,
            image_data, pdf_content, language
        )

        # Return PDF as response with forced download
        filename = f"medical_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        return Response(
            content=pdf_bytes,
            media_type='application/pdf',
            headers={
                'Content-Disposition': f'attachment; filename="{filename}"',
                'Content-Type': 'application/pdf',
                'Cache-Control': 'no-cache'
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating PDF: {str(e)}")

# Database initialization is now handled in the lifespan event above

# Health check endpoint
@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "MedCode AI API is running"}

# Simple test endpoint that doesn't require database
@app.get("/")
def root():
    """Root endpoint"""
    return {
        "message": "Welcome to MedCode AI API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

# Test AI endpoint without database dependency
@app.post("/test-ai")
def test_ai(query: str = "What is diabetes?", language: str = "English"):
    """Test AI functionality without database"""
    try:
        role_prompt = role_prompts.get("Common User", "")

        # Add language instruction if not English
        if language and language != "English":
            language_instruction = f"""

CRITICAL LANGUAGE REQUIREMENT:
- You MUST respond entirely in {language} language
- Do NOT use any English words in your response
- Translate all medical terms to {language}
- The user specifically requested a response in {language}, so this is mandatory
            """
            role_prompt += language_instruction

        prompt_parts = [query, role_prompt]
        response = model.generate_content(prompt_parts)
        if response:
            return {"query": query, "language": language, "response": response.text}
        else:
            return {"error": "AI model failed to generate response"}
    except Exception as e:
        return {"error": f"AI test failed: {str(e)}"}

# Database Functions

def init_db():
    """
    Initialize the database and create necessary tables if they don't exist
    """
    conn = get_db_connection()
    if conn:
        try:
            # Create a cursor
            cur = conn.cursor()

            # Create users table
            cur.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id SERIAL PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    name VARCHAR(100) NOT NULL,
                    email VARCHAR(100) NOT NULL,
                    role VARCHAR(20) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Create query_history table
            cur.execute('''
                CREATE TABLE IF NOT EXISTS query_history (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id),
                    query TEXT NOT NULL,
                    role VARCHAR(20) NOT NULL,
                    has_image BOOLEAN DEFAULT FALSE,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Commit changes
            conn.commit()

            # Close cursor
            cur.close()

            return conn
        except psycopg2.Error as e:
            print(f"Database initialization error: {e}")
            if conn:
                conn.close()
            raise Exception(f"Failed to initialize database: {e}")
    else:
        print("No database connection available - skipping database initialization")
        return None

def verify_credentials(username, password):
    """
    Verify user credentials against the database

    Args:
        username (str): The username to verify
        password (str): The password to verify

    Returns:
        tuple: (authenticated, role, name, user_id) where authenticated is a boolean
    """
    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("SELECT id, password, role, name FROM users WHERE username=%s", (username,))
                result = cur.fetchone()

                if result:
                    stored_password = result['password']
                    role = result['role']
                    name = result['name']
                    user_id = result['id']

                    if stored_password == hashlib.sha256(password.encode()).hexdigest():
                        conn.close()
                        return True, role, name, user_id
            conn.close()
        except psycopg2.Error as e:
            print(f"Authentication error: {e}")
            if conn:
                conn.close()

    return False, None, None, None

def create_account(username, password, name, email, role):
    """
    Create a new user account

    Args:
        username (str): Username for the new account
        password (str): Password for the new account
        name (str): Full name of the user
        email (str): Email address of the user
        role (str): Role of the user (Student, Doctor, Common User)

    Returns:
        tuple: (success, message) where success is a boolean
    """
    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor() as cur:
                # Check if username already exists
                cur.execute("SELECT username FROM users WHERE username=%s", (username,))
                if cur.fetchone():
                    conn.close()
                    return False, "Username already exists"

                # Create new user
                hashed_pw = hashlib.sha256(password.encode()).hexdigest()
                cur.execute("INSERT INTO users (username, password, name, email, role) VALUES (%s, %s, %s, %s, %s)",
                          (username, hashed_pw, name, email, role))
                conn.commit()
                conn.close()
                return True, "Account created successfully!"
        except psycopg2.Error as e:
            if conn:
                conn.close()
            return False, f"Error creating account: {str(e)}"

    return False, "Database connection error"

def update_profile(user_id, name=None, email=None):
    """
    Update user profile information

    Args:
        user_id (int): User ID to update
        name (str, optional): New name
        email (str, optional): New email

    Returns:
        tuple: (success, message) where success is a boolean
    """
    if not name and not email:
        return False, "No updates provided"

    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor() as cur:
                updates = []
                params = []

                if name:
                    updates.append("name=%s")
                    params.append(name)
                if email:
                    updates.append("email=%s")
                    params.append(email)

                params.append(user_id)

                query = f"UPDATE users SET {', '.join(updates)} WHERE id=%s"
                cur.execute(query, params)
                conn.commit()
                conn.close()
                return True, "Profile updated successfully!"
        except psycopg2.Error as e:
            if conn:
                conn.close()
            return False, f"Error updating profile: {str(e)}"

    return False, "Database connection error"

def change_password(user_id, current_password, new_password):
    """
    Change user password

    Args:
        user_id (int): User ID
        current_password (str): Current password
        new_password (str): New password

    Returns:
        tuple: (success, message) where success is a boolean
    """
    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor() as cur:
                # Verify current password
                cur.execute("SELECT password FROM users WHERE id=%s", (user_id,))
                result = cur.fetchone()

                if result and result[0] == hashlib.sha256(current_password.encode()).hexdigest():
                    # Update password
                    hashed_new_pw = hashlib.sha256(new_password.encode()).hexdigest()
                    cur.execute("UPDATE users SET password=%s WHERE id=%s",
                              (hashed_new_pw, user_id))
                    conn.commit()
                    conn.close()
                    return True, "Password changed successfully!"
                else:
                    conn.close()
                    return False, "Current password is incorrect"
        except psycopg2.Error as e:
            if conn:
                conn.close()
            return False, f"Error changing password: {str(e)}"

    return False, "Database connection error"

def save_query_history(user_id, query, role, has_image=False):
    """
    Save user query to history

    Args:
        user_id (int): User ID
        query (str): The user's query
        role (str): The role used for the query
        has_image (bool): Whether the query included an image

    Returns:
        bool: Success or failure
    """
    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor() as cur:
                cur.execute(
                    "INSERT INTO query_history (user_id, query, role, has_image) VALUES (%s, %s, %s, %s)",
                    (user_id, query, role, has_image)
                )
                conn.commit()
                conn.close()
                return True
        except psycopg2.Error as e:
            print(f"Error saving query history: {e}")
            if conn:
                conn.close()

    return False

def get_user_query_history(user_id, limit=20):
    """
    Retrieve query history for a specific user

    Args:
        user_id (int): User ID
        limit (int): Maximum number of records to return

    Returns:
        list: List of query history records
    """
    conn = get_db_connection()
    if not conn:
        print("No database connection available")
        return []

    try:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute(
            "SELECT id, query, role, timestamp, has_image FROM query_history " +
            "WHERE user_id=%s ORDER BY timestamp DESC LIMIT %s",
            (user_id, limit)
        )
        history = cur.fetchall()
        cur.close()
        conn.close()
        print(f"Retrieved {len(history)} history records for user {user_id}")
        return history
    except psycopg2.Error as e:
        print(f"Database error retrieving query history: {e}")
        if conn:
            conn.close()
        return []
    except Exception as e:
        print(f"Unexpected error retrieving query history: {e}")
        if conn:
            conn.close()
        return []

def get_user_stats(user_id):
    """
    Get user statistics

    Args:
        user_id (int): User ID

    Returns:
        dict: Dictionary containing user statistics
    """
    conn = get_db_connection()
    stats = {
        "total_queries": 0,
        "queries_today": 0,
        "days_active": 0
    }

    if conn:
        try:
            with conn.cursor() as cur:
                # Total queries
                cur.execute("SELECT COUNT(*) FROM query_history WHERE user_id=%s", (user_id,))
                stats["total_queries"] = cur.fetchone()[0]

                # Queries today
                cur.execute(
                    "SELECT COUNT(*) FROM query_history WHERE user_id=%s AND DATE(timestamp) = CURRENT_DATE",
                    (user_id,)
                )
                stats["queries_today"] = cur.fetchone()[0]

                # Days active
                cur.execute(
                    "SELECT COUNT(DISTINCT DATE(timestamp)) FROM query_history WHERE user_id=%s",
                    (user_id,)
                )
                stats["days_active"] = cur.fetchone()[0]

                conn.close()
                return stats
        except psycopg2.Error as e:
            print(f"Error retrieving user stats: {e}")
            if conn:
                conn.close()

    return stats

# PubMed Research Functions

def fetch_medical_articles_sync(query, num_articles=5):
    """Fetch medical articles from PubMed synchronously"""
    url = f"https://pubmed.ncbi.nlm.nih.gov/?term={query.replace(' ', '+')}"
    headers = {"User-Agent": "Mozilla/5.0"}
    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.text, "html.parser")

    articles = []
    for article in soup.find_all("a", class_="docsum-title", limit=num_articles):
        title = article.get_text(strip=True)
        link = "https://pubmed.ncbi.nlm.nih.gov" + article.get("href", "")
        articles.append(f"🔹 [{title}]({link})")
    return "\n".join(articles) if articles else "No relevant research papers found."

async def fetch_medical_articles_async(query, num_articles=5):
    """Fetch medical articles from PubMed asynchronously"""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, fetch_medical_articles_sync, query, num_articles)

def fetch_medical_articles(query, num_articles=5):
    """Fetch medical articles with asyncio handling"""
    try:
        return asyncio.run(fetch_medical_articles_async(query, num_articles))
    except RuntimeError:
        # If already in an event loop, fallback to sync
        return fetch_medical_articles_sync(query, num_articles)

# PDF Processing Functions

def extract_text_from_pdf_bytes(pdf_bytes):
    """
    Extract text from PDF bytes

    Args:
        pdf_bytes (bytes): The PDF file as bytes

    Returns:
        str: Extracted text from the PDF
    """
    pdf_text = ""
    try:
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(pdf_bytes))
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            pdf_text += page.extract_text() + "\n\n"
        return pdf_text
    except Exception as e:
        return f"Error extracting text from PDF: {str(e)}"

def extract_text_from_pdf(pdf_file):
    """
    Extract text from a PDF file (for Streamlit file uploader)

    Args:
        pdf_file: The uploaded PDF file

    Returns:
        str: Extracted text from the PDF
    """
    pdf_text = ""
    try:
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(pdf_file.getvalue()))
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            pdf_text += page.extract_text() + "\n\n"
        return pdf_text
    except Exception as e:
        return f"Error extracting text from PDF: {str(e)}"

def create_medical_pdf(user_name, role, query, response_text, image_data=None, pdf_content=None, language="English"):
    """
    Create a PDF document containing the medical query and response

    Args:
        user_name (str): Name of the user
        role (str): Selected role (Student, Doctor, Common User)
        query (str): The user's query
        response_text (str): The AI response text
        image_data (bytes, optional): Image data if an image was uploaded
        pdf_content (str, optional): Content extracted from uploaded PDF
        language (str, optional): Language of the response

    Returns:
        bytes: PDF file as bytes
    """
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=letter, topMargin=0.5*inch)

    # Get styles
    styles = getSampleStyleSheet()

    # Custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    )

    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=12,
        textColor=colors.darkgreen
    )

    subheading_style = ParagraphStyle(
        'CustomSubheading',
        parent=styles['Heading3'],
        fontSize=12,
        spaceAfter=8,
        textColor=colors.darkred
    )

    normal_style = styles['Normal']
    normal_style.fontSize = 10
    normal_style.spaceAfter = 6

    # Content list
    content = []

    # Title
    content.append(Paragraph("MedCode AI - Medical Analysis Report", title_style))
    content.append(Spacer(1, 0.3*inch))

    # User info
    content.append(Paragraph(f"Patient/User: {user_name}", normal_style))
    content.append(Paragraph(f"Role: {role}", normal_style))
    content.append(Paragraph(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", normal_style))
    content.append(Paragraph(f"Language: {language}", normal_style))
    content.append(Spacer(1, 0.2*inch))

    # Query section
    content.append(Paragraph("Query:", heading_style))
    content.append(Paragraph(query, normal_style))
    content.append(Spacer(1, 0.3*inch))

    # Add image if provided
    if image_data:
        try:
            img_buffer = io.BytesIO(image_data)
            img = ReportLabImage(img_buffer, width=4*inch, height=3*inch)
            content.append(Paragraph("Uploaded Image:", subheading_style))
            content.append(img)
            content.append(Spacer(1, 0.3*inch))
        except Exception as e:
            content.append(Paragraph(f"Error displaying image: {str(e)}", normal_style))

    # Add PDF content if provided
    if pdf_content:
        content.append(Paragraph("Uploaded PDF Content:", subheading_style))
        # Limit PDF content to avoid very large PDFs
        pdf_preview = pdf_content[:1000] + "..." if len(pdf_content) > 1000 else pdf_content
        content.append(Paragraph(pdf_preview, normal_style))
        content.append(Spacer(1, 0.3*inch))

    # Response section
    content.append(Paragraph("Medical Analysis:", heading_style))

    # Convert markdown/HTML to plain text for PDF
    plain_text = html2text(response_text)
    paragraphs = plain_text.split('\n\n')

    for para in paragraphs:
        if para.strip():
            content.append(Paragraph(para.replace('\n', '<br/>'), normal_style))

    # Build the PDF
    doc.build(content)

    # Get the PDF as bytes
    pdf_bytes = buffer.getvalue()
    buffer.close()

    return pdf_bytes

def get_pdf_download_link(pdf_bytes, filename="medical_analysis.pdf"):
    """
    Generate a download link for the PDF

    Args:
        pdf_bytes (bytes): The PDF file as bytes
        filename (str): Name of the file to download

    Returns:
        str: HTML string with download link
    """
    b64 = base64.b64encode(pdf_bytes).decode()
    href = f'<a href="data:application/pdf;base64,{b64}" download="{filename}" ' \
           f'style="text-decoration:none; color:white; padding:10px 25px; ' \
           f'background-color:#0062E6; border-radius:30px; ' \
           f'box-shadow:0 8px 15px rgba(0,0,0,0.1); ' \
           f'display:inline-block; font-weight:bold; ' \
           f'transition:all 0.3s; text-transform:uppercase; ' \
           f'letter-spacing:1px;">' \
           f'📥 Download PDF</a>'
    return href

# Utility Functions

def create_env_file_template():
    """
    Create a template .env file for database configuration
    """
    if not os.path.exists('.env'):
        with open('.env', 'w') as f:
            f.write('''# PostgreSQL Database Configuration
DB_HOST=localhost
DB_NAME=medcodeai
DB_USER=postgres
DB_PASSWORD=postges
DB_PORT=5432
''')
        return True
    return False

# Main function to run the FastAPI server
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
