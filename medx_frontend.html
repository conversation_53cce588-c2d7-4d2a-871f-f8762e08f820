<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MedX AI - Medical Analysis</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .form-container {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .file-upload {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-upload input[type=file] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-upload-label {
            display: block;
            padding: 15px;
            border: 2px dashed #4facfe;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9ff;
        }

        .file-upload-label:hover {
            background: #e8f2ff;
            border-color: #2196F3;
        }

        .generate-btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(0,0,0,0.15);
        }

        .generate-btn:active {
            transform: translateY(0);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .response {
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
            display: none;
        }

        .response.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .response.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .response.loading {
            background: #e2f3ff;
            border: 1px solid #b8daff;
            color: #004085;
            text-align: center;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .role-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #4facfe;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 MedX AI</h1>
            <p>Advanced Medical Analysis & PDF Generation</p>
        </div>

        <div class="form-container">
            <form id="medicalForm">
                <div class="form-group">
                    <label for="user_name">👤 Your Name:</label>
                    <input type="text" id="user_name" name="user_name" required placeholder="Enter your full name">
                </div>

                <div class="form-group">
                    <label for="role">👨‍⚕️ Select Your Role:</label>
                    <select id="role" name="role" required>
                        <option value="">Choose your role...</option>
                        <option value="Student">🎓 Medical Student</option>
                        <option value="Doctor">👨‍⚕️ Doctor</option>
                        <option value="Common User">👤 General User</option>
                    </select>
                </div>

                <div class="role-info" id="roleInfo" style="display: none;">
                    <strong>Role Selected:</strong> <span id="selectedRole"></span>
                    <br><small id="roleDescription"></small>
                </div>

                <div class="form-group">
                    <label for="query">🔍 Medical Query:</label>
                    <textarea id="query" name="query" required 
                              placeholder="Enter your medical question or describe your symptoms...&#10;&#10;Examples:&#10;• What are the latest treatments for diabetes?&#10;• Explain the symptoms of hypertension&#10;• How to manage chronic pain?"></textarea>
                </div>

                <div class="form-group">
                    <label for="language">🌐 Response Language:</label>
                    <select id="language" name="language">
                        <option value="English" selected>🇺🇸 English</option>
                        <option value="Spanish">🇪🇸 Spanish</option>
                        <option value="French">🇫🇷 French</option>
                        <option value="German">🇩🇪 German</option>
                        <option value="Italian">🇮🇹 Italian</option>
                        <option value="Portuguese">🇵🇹 Portuguese</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>📎 Upload Medical File (Optional):</label>
                    <div class="file-upload">
                        <input type="file" id="file" name="file" accept=".jpg,.jpeg,.png,.pdf">
                        <label for="file" class="file-upload-label">
                            📁 Click to upload image or PDF<br>
                            <small>Supported: JPG, PNG, PDF</small>
                        </label>
                    </div>
                </div>

                <button type="submit" class="generate-btn" id="generateBtn">
                    🚀 Generate Medical Analysis & Download PDF
                </button>
            </form>

            <div id="response" class="response"></div>
        </div>

        <div class="footer">
            <p>© 2025 MedX AI | Advanced Medical Analysis Platform</p>
            <small>For educational and informational purposes only. Always consult healthcare professionals.</small>
        </div>
    </div>

    <script>
        // Role descriptions
        const roleDescriptions = {
            'Student': 'Get detailed explanations, case studies, and research paper references',
            'Doctor': 'Access professional medical insights and treatment recommendations',
            'Common User': 'Receive easy-to-understand health information and guidance'
        };

        // Handle role selection
        document.getElementById('role').addEventListener('change', function() {
            const roleInfo = document.getElementById('roleInfo');
            const selectedRole = document.getElementById('selectedRole');
            const roleDescription = document.getElementById('roleDescription');
            
            if (this.value) {
                selectedRole.textContent = this.value;
                roleDescription.textContent = roleDescriptions[this.value];
                roleInfo.style.display = 'block';
            } else {
                roleInfo.style.display = 'none';
            }
        });

        // Handle file upload display
        document.getElementById('file').addEventListener('change', function() {
            const label = document.querySelector('.file-upload-label');
            if (this.files.length > 0) {
                const fileName = this.files[0].name;
                label.innerHTML = `✅ Selected: ${fileName}<br><small>Click to change file</small>`;
                label.style.background = '#e8f5e8';
                label.style.borderColor = '#28a745';
            } else {
                label.innerHTML = '📁 Click to upload image or PDF<br><small>Supported: JPG, PNG, PDF</small>';
                label.style.background = '#f8f9ff';
                label.style.borderColor = '#4facfe';
            }
        });

        // Handle form submission
        document.getElementById('medicalForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const responseDiv = document.getElementById('response');
            const generateBtn = document.getElementById('generateBtn');
            
            try {
                // Show loading state
                responseDiv.style.display = 'block';
                responseDiv.className = 'response loading';
                responseDiv.innerHTML = `
                    <div class="loading-spinner"></div>
                    <strong>🔄 Processing your medical query...</strong>
                    <br><small>Generating AI analysis and preparing PDF download...</small>
                `;
                generateBtn.disabled = true;
                generateBtn.textContent = '⏳ Processing...';

                // First, get AI response
                const aiResponse = await fetch('http://localhost:8000/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: 1,
                        query: formData.get('query'),
                        role: formData.get('role'),
                        has_image: formData.get('file') ? true : false
                    })
                });

                if (!aiResponse.ok) {
                    throw new Error(`AI Response failed: ${aiResponse.status}`);
                }

                const aiResult = await aiResponse.json();

                // Update loading message
                responseDiv.innerHTML = `
                    <div class="loading-spinner"></div>
                    <strong>✅ Analysis complete! Generating PDF...</strong>
                    <br><small>Your PDF will download automatically...</small>
                `;

                // Now generate and download PDF
                const pdfFormData = new FormData();
                pdfFormData.append('user_name', formData.get('user_name'));
                pdfFormData.append('role', formData.get('role'));
                pdfFormData.append('query', formData.get('query'));
                pdfFormData.append('response_text', aiResult.response);
                pdfFormData.append('language', formData.get('language'));
                
                if (formData.get('file')) {
                    pdfFormData.append('file', formData.get('file'));
                }

                const pdfResponse = await fetch('http://localhost:8000/generate-pdf', {
                    method: 'POST',
                    body: pdfFormData
                });

                if (pdfResponse.ok) {
                    // Auto-download PDF
                    const blob = await pdfResponse.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `MedX_Analysis_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.pdf`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    // Show success message
                    responseDiv.className = 'response success';
                    responseDiv.innerHTML = `
                        <h3>🎉 Success!</h3>
                        <p><strong>✅ Medical Analysis Generated</strong></p>
                        <p><strong>📥 PDF Downloaded Automatically</strong></p>
                        <hr style="margin: 15px 0;">
                        <h4>📋 AI Response Preview:</h4>
                        <div style="max-height: 200px; overflow-y: auto; background: white; padding: 15px; border-radius: 5px; margin-top: 10px;">
                            ${aiResult.response.replace(/\n/g, '<br>')}
                        </div>
                        <p style="margin-top: 15px;"><small>💡 Check your Downloads folder for the complete PDF report!</small></p>
                    `;
                } else {
                    throw new Error(`PDF generation failed: ${pdfResponse.status}`);
                }

            } catch (error) {
                responseDiv.className = 'response error';
                responseDiv.innerHTML = `
                    <h3>❌ Error</h3>
                    <p><strong>Something went wrong:</strong></p>
                    <p>${error.message}</p>
                    <hr style="margin: 15px 0;">
                    <p><strong>🔧 Troubleshooting:</strong></p>
                    <ul style="margin-left: 20px;">
                        <li>Make sure your backend is running on <code>http://localhost:8000</code></li>
                        <li>Check your internet connection</li>
                        <li>Verify all required fields are filled</li>
                        <li>Try refreshing the page and submitting again</li>
                    </ul>
                `;
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = '🚀 Generate Medical Analysis & Download PDF';
            }
        });
    </script>
</body>
</html>
