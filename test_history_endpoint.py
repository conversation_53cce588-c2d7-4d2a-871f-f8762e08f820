#!/usr/bin/env python3
"""
Test script to add sample data and test the history endpoint
"""

import os
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
import requests
import json

def add_sample_history():
    """Add some sample query history data"""
    print("🔍 Adding sample query history data...")
    
    # Load environment variables
    load_dotenv()
    
    # Get database configuration
    db_host = os.getenv("DB_HOST", "localhost")
    db_name = os.getenv("DB_NAME", "medcodeai")
    db_user = os.getenv("DB_USER", "postgres")
    db_password = os.getenv("DB_PASSWORD", "")
    db_port = os.getenv("DB_PORT", "5432")
    
    try:
        # Connect to database
        database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()
        
        # Check if we have users
        cur.execute("SELECT id, username FROM users LIMIT 3")
        users = cur.fetchall()
        
        if not users:
            print("❌ No users found. Please create a user first.")
            return False
            
        print(f"✅ Found {len(users)} users:")
        for user in users:
            print(f"   User ID: {user[0]}, Username: {user[1]}")
        
        # Add sample query history for the first user
        user_id = users[0][0]
        
        sample_queries = [
            ("What are the symptoms of diabetes?", "Common User", False),
            ("Explain hypertension treatment options", "Common User", False),
            ("How to manage cholesterol levels?", "Common User", False),
            ("What is the difference between Type 1 and Type 2 diabetes?", "Student", False),
            ("Latest guidelines for heart disease prevention", "Doctor", True)
        ]
        
        print(f"\n📝 Adding sample queries for user ID {user_id}...")
        
        for query, role, has_image in sample_queries:
            cur.execute(
                "INSERT INTO query_history (user_id, query, role, has_image) VALUES (%s, %s, %s, %s)",
                (user_id, query, role, has_image)
            )
        
        conn.commit()
        print(f"✅ Added {len(sample_queries)} sample queries")
        
        # Verify the data
        cur.execute("SELECT COUNT(*) FROM query_history WHERE user_id = %s", (user_id,))
        count = cur.fetchone()[0]
        print(f"✅ Total queries for user {user_id}: {count}")
        
        cur.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error adding sample data: {e}")
        return False

def test_history_endpoint():
    """Test the history endpoint"""
    print("\n🧪 Testing history endpoint...")
    
    try:
        # Test the endpoint
        response = requests.get("http://localhost:8000/history/1")
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ History endpoint working!")
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to the API. Make sure the backend is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")

def main():
    print("🚀 Testing History Endpoint Setup")
    print("=" * 50)
    
    # Add sample data
    if add_sample_history():
        # Test the endpoint
        test_history_endpoint()
    else:
        print("❌ Failed to add sample data. Cannot test endpoint.")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
