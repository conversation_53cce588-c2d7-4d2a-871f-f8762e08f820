#!/usr/bin/env python3
"""
Test database connection for MedCode AI
Run this script to verify your PostgreSQL connection is working
"""

import os
import psycopg2
from dotenv import load_dotenv

def test_database_connection():
    """Test the database connection and setup"""
    print("🔍 Testing MedCode AI Database Connection...")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Get database configuration
    db_host = os.getenv("DB_HOST", "localhost")
    db_name = os.getenv("DB_NAME", "medcodeai")
    db_user = os.getenv("DB_USER", "postgres")
    db_password = os.getenv("DB_PASSWORD", "")
    db_port = os.getenv("DB_PORT", "5432")
    
    print(f"📊 Database Configuration:")
    print(f"   Host: {db_host}")
    print(f"   Database: {db_name}")
    print(f"   User: {db_user}")
    print(f"   Port: {db_port}")
    print(f"   Password: {'✅ Set' if db_password else '❌ Not set'}")
    print()
    
    if not db_password:
        print("❌ ERROR: DB_PASSWORD not set in .env file")
        print("Please update your .env file with your PostgreSQL password")
        return False
    
    try:
        # Test connection
        print("🔌 Attempting to connect to database...")
        database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        conn = psycopg2.connect(database_url)
        
        print("✅ Database connection successful!")
        
        # Test tables
        print("\n📋 Checking database tables...")
        cur = conn.cursor()
        
        # Check if tables exist
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        tables = cur.fetchall()
        
        if tables:
            print("✅ Found tables:")
            for table in tables:
                print(f"   - {table[0]}")
        else:
            print("⚠️  No tables found. Creating tables...")
            create_tables(cur, conn)
        
        # Test users table
        print("\n👥 Checking users table...")
        cur.execute("SELECT COUNT(*) FROM users")
        user_count = cur.fetchone()[0]
        print(f"✅ Users table has {user_count} records")
        
        if user_count == 0:
            print("⚠️  No users found. Creating test user...")
            create_test_user(cur, conn)
        
        # Show sample users
        cur.execute("SELECT id, username, name, role FROM users LIMIT 3")
        users = cur.fetchall()
        print("\n📝 Sample users:")
        for user in users:
            print(f"   ID: {user[0]}, Username: {user[1]}, Name: {user[2]}, Role: {user[3]}")
        
        cur.close()
        conn.close()
        
        print("\n🎉 Database setup is complete and working!")
        print("✅ You can now use all database-related API endpoints")
        return True
        
    except psycopg2.OperationalError as e:
        print(f"❌ Database connection failed: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Make sure PostgreSQL is running")
        print("2. Check your .env file has the correct password")
        print("3. Verify the database 'medcodeai' exists")
        print("4. Check if the postgres user has the right permissions")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def create_tables(cur, conn):
    """Create the required tables"""
    try:
        # Create users table
        cur.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL,
                role VARCHAR(20) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create query_history table
        cur.execute('''
            CREATE TABLE IF NOT EXISTS query_history (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id),
                query TEXT NOT NULL,
                role VARCHAR(20) NOT NULL,
                has_image BOOLEAN DEFAULT FALSE,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        print("✅ Tables created successfully")
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        conn.rollback()

def create_test_user(cur, conn):
    """Create a test user"""
    try:
        import hashlib
        
        # Create test user with hashed password
        password_hash = hashlib.sha256("testpassword".encode()).hexdigest()
        
        cur.execute('''
            INSERT INTO users (username, password, name, email, role) 
            VALUES (%s, %s, %s, %s, %s)
        ''', ('testuser', password_hash, 'Test User', '<EMAIL>', 'Common User'))
        
        conn.commit()
        print("✅ Test user created (username: testuser, password: testpassword)")
        
    except Exception as e:
        print(f"❌ Error creating test user: {e}")
        conn.rollback()

if __name__ == "__main__":
    test_database_connection()
    input("\nPress Enter to exit...")
