<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MedCode AI - File Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            margin-top: 0;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 MedCode AI - File Upload Testing</h1>
        
        <!-- Query with File Upload Section -->
        <div class="section">
            <h2>📄 Query with File Upload</h2>
            <form id="queryForm">
                <div class="form-group">
                    <label for="user_id">User ID:</label>
                    <input type="number" id="user_id" name="user_id" value="1" required>
                </div>
                
                <div class="form-group">
                    <label for="query">Medical Query:</label>
                    <textarea id="query" name="query" placeholder="Enter your medical question..." required>Please analyze this medical report and provide recommendations</textarea>
                </div>
                
                <div class="form-group">
                    <label for="role">Role:</label>
                    <select id="role" name="role" required>
                        <option value="Common User">Common User</option>
                        <option value="Student">Student</option>
                        <option value="Doctor" selected>Doctor</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="language">Language:</label>
                    <select id="language" name="language" required>
                        <option value="English" selected>English</option>
                        <option value="Spanish">Spanish</option>
                        <option value="French">French</option>
                        <option value="German">German</option>
                        <option value="Italian">Italian</option>
                        <option value="Portuguese">Portuguese</option>
                        <option value="Hindi">Hindi</option>
                        <option value="Arabic">Arabic</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="file">Upload Medical File:</label>
                    <input type="file" id="file" name="file" accept=".pdf,.txt,.jpg,.png,.jpeg" required>
                </div>
                
                <button type="submit">🔍 Analyze File</button>
            </form>
            
            <div id="queryResponse" class="response"></div>
        </div>
        
        <!-- PDF Generation Section -->
        <div class="section">
            <h2>📋 Generate PDF Report</h2>
            <form id="pdfForm">
                <div class="form-group">
                    <label for="user_name">User Name:</label>
                    <input type="text" id="user_name" name="user_name" value="Dr. Smith" required>
                </div>
                
                <div class="form-group">
                    <label for="pdf_role">Role:</label>
                    <select id="pdf_role" name="role" required>
                        <option value="Common User">Common User</option>
                        <option value="Student">Student</option>
                        <option value="Doctor" selected>Doctor</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="pdf_query">Query:</label>
                    <input type="text" id="pdf_query" name="query" value="Generate a diabetes management report" required>
                </div>
                
                <div class="form-group">
                    <label for="response_text">Response Text:</label>
                    <textarea id="response_text" name="response_text" required>Diabetes management involves regular monitoring of blood glucose levels, maintaining a healthy diet, regular exercise, and medication compliance. Key recommendations include: 1) Monitor blood sugar 2-3 times daily, 2) Follow a low-carb, high-fiber diet, 3) Exercise 30 minutes daily, 4) Take medications as prescribed, 5) Regular check-ups with healthcare provider.</textarea>
                </div>
                
                <div class="form-group">
                    <label for="pdf_language">Language:</label>
                    <select id="pdf_language" name="language" required>
                        <option value="English" selected>English</option>
                        <option value="Spanish">Spanish</option>
                        <option value="French">French</option>
                        <option value="German">German</option>
                    </select>
                </div>
                
                <button type="submit">📄 Generate PDF</button>
            </form>
            
            <div id="pdfResponse" class="response"></div>
        </div>
    </div>

    <script>
        // Query with File Upload
        document.getElementById('queryForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const responseDiv = document.getElementById('queryResponse');
            
            try {
                responseDiv.style.display = 'block';
                responseDiv.className = 'response';
                responseDiv.innerHTML = '⏳ Processing your request...';
                
                const response = await fetch('http://localhost:8000/query-with-file', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    responseDiv.className = 'response success';
                    responseDiv.innerHTML = `
                        <h3>✅ Analysis Complete!</h3>
                        <p><strong>Response:</strong></p>
                        <p>${result.response}</p>
                    `;
                } else {
                    const error = await response.text();
                    responseDiv.className = 'response error';
                    responseDiv.innerHTML = `
                        <h3>❌ Error</h3>
                        <p>Status: ${response.status}</p>
                        <p>${error}</p>
                    `;
                }
            } catch (error) {
                responseDiv.className = 'response error';
                responseDiv.innerHTML = `
                    <h3>❌ Connection Error</h3>
                    <p>Make sure your backend is running on http://localhost:8000</p>
                    <p>Error: ${error.message}</p>
                `;
            }
        });
        
        // PDF Generation
        document.getElementById('pdfForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const responseDiv = document.getElementById('pdfResponse');
            
            try {
                responseDiv.style.display = 'block';
                responseDiv.className = 'response';
                responseDiv.innerHTML = '⏳ Generating PDF...';
                
                const response = await fetch('http://localhost:8000/generate-pdf', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    // Download the PDF
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'medical_report.pdf';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    responseDiv.className = 'response success';
                    responseDiv.innerHTML = `
                        <h3>✅ PDF Generated Successfully!</h3>
                        <p>The PDF has been downloaded to your computer.</p>
                    `;
                } else {
                    const error = await response.text();
                    responseDiv.className = 'response error';
                    responseDiv.innerHTML = `
                        <h3>❌ Error</h3>
                        <p>Status: ${response.status}</p>
                        <p>${error}</p>
                    `;
                }
            } catch (error) {
                responseDiv.className = 'response error';
                responseDiv.innerHTML = `
                    <h3>❌ Connection Error</h3>
                    <p>Make sure your backend is running on http://localhost:8000</p>
                    <p>Error: ${error.message}</p>
                `;
            }
        });
    </script>
</body>
</html>
