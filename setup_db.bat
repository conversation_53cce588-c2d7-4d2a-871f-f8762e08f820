@echo off
echo Setting up MedCode AI Database...
echo.

echo Step 1: Creating database and tables...
psql -U postgres -f setup_database.sql

echo.
echo Step 2: Verifying database setup...
psql -U postgres -d medcodeai -c "\dt"

echo.
echo Step 3: Showing test users...
psql -U postgres -d medcodeai -c "SELECT id, username, name, email, role, created_at FROM users;"

echo.
echo Database setup complete!
echo.
echo Next steps:
echo 1. Update the .env file with your PostgreSQL password
echo 2. Restart your FastAPI backend
echo 3. Test the database endpoints
echo.
pause
