#!/usr/bin/env python3
"""
Test script for file upload endpoints
This script tests both query-with-file and generate-pdf endpoints
"""

import requests
import os
from io import Bytes<PERSON>
from reportlab.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph
from reportlab.lib.styles import getSampleStyleSheet

def create_test_pdf():
    """Create a simple test PDF file"""
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=letter)
    styles = getSampleStyleSheet()
    
    content = [
        Paragraph("Medical Test Report", styles['Title']),
        Paragraph("Patient: John Doe", styles['Normal']),
        Paragraph("Date: 2024-01-15", styles['Normal']),
        Paragraph("", styles['Normal']),
        Paragraph("Test Results:", styles['Heading2']),
        Paragraph("Blood Pressure: 120/80 mmHg (Normal)", styles['Normal']),
        Paragraph("Blood Sugar: 95 mg/dL (Normal)", styles['Normal']),
        Paragraph("Cholesterol: 180 mg/dL (Normal)", styles['Normal']),
        Paragraph("", styles['Normal']),
        Paragraph("Recommendations:", styles['Heading2']),
        Paragraph("Continue current lifestyle and diet.", styles['Normal']),
        Paragraph("Schedule follow-up in 6 months.", styles['Normal']),
    ]
    
    doc.build(content)
    buffer.seek(0)
    return buffer

def test_query_with_file():
    """Test the query-with-file endpoint"""
    print("🧪 Testing Query with File Upload...")
    
    # Create test PDF
    pdf_buffer = create_test_pdf()
    
    # Prepare the request
    url = "http://localhost:8000/query-with-file"
    
    # Form data
    data = {
        'user_id': 1,
        'query': 'Please analyze this medical report and provide recommendations',
        'role': 'Doctor',
        'language': 'English'
    }
    
    # File data
    files = {
        'file': ('test_medical_report.pdf', pdf_buffer, 'application/pdf')
    }
    
    try:
        response = requests.post(url, data=data, files=files)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Query with file upload successful!")
            print(f"Response: {result['response'][:200]}...")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure backend is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_generate_pdf():
    """Test the generate-pdf endpoint"""
    print("\n🧪 Testing PDF Generation...")
    
    url = "http://localhost:8000/generate-pdf"
    
    # Form data
    data = {
        'user_name': 'Dr. Smith',
        'role': 'Doctor',
        'query': 'Generate a diabetes management report',
        'response_text': 'Diabetes management involves regular monitoring of blood glucose levels, maintaining a healthy diet, regular exercise, and medication compliance. Key recommendations include: 1) Monitor blood sugar 2-3 times daily, 2) Follow a low-carb, high-fiber diet, 3) Exercise 30 minutes daily, 4) Take medications as prescribed, 5) Regular check-ups with healthcare provider.',
        'language': 'English'
    }
    
    try:
        response = requests.post(url, data=data)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            # Save the PDF file
            with open('generated_medical_report.pdf', 'wb') as f:
                f.write(response.content)
            print("✅ PDF generation successful!")
            print("📄 PDF saved as 'generated_medical_report.pdf'")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure backend is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    print("🚀 Testing File Upload Endpoints")
    print("=" * 50)
    
    # Test query with file
    test_query_with_file()
    
    # Test PDF generation
    test_generate_pdf()
    
    print("\n✅ File upload testing complete!")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
