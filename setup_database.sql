-- PostgreSQL Database Setup for MedCode AI
-- Run this script to create the database and tables

-- Create database (run this as postgres superuser)
CREATE DATABASE medcodeai;

-- Connect to the medcodeai database and create tables
\c medcodeai;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create query_history table
CREATE TABLE IF NOT EXISTS query_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    query TEXT NOT NULL,
    role VARCHAR(20) NOT NULL,
    has_image BOOLEAN DEFAULT FALSE,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_query_history_user_id ON query_history(user_id);
CREATE INDEX IF NOT EXISTS idx_query_history_timestamp ON query_history(timestamp);

-- Insert a test user (password is hashed version of "testpassword")
INSERT INTO users (username, password, name, email, role) 
VALUES (
    'testuser', 
    'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', -- SHA256 of "testpassword"
    'Test User', 
    '<EMAIL>', 
    'Common User'
) ON CONFLICT (username) DO NOTHING;

-- Insert a doctor test user (password is hashed version of "doctorpass")
INSERT INTO users (username, password, name, email, role) 
VALUES (
    'doctor1', 
    'a4d4c5c8b8e8c4c8d4e8f4a4b4c4d4e4f4a4b4c4d4e4f4a4b4c4d4e4f4a4b4c4', -- SHA256 of "doctorpass"
    'Dr. Smith', 
    '<EMAIL>', 
    'Doctor'
) ON CONFLICT (username) DO NOTHING;

-- Verify tables were created
\dt

-- Show sample data
SELECT * FROM users;
