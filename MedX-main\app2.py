# FastAPI Backend for MedCode AI
import requests
from bs4 import BeautifulSoup
import google.generativeai as genai
from google_api_key import google_api_key
import hashlib
import psycopg2
import os
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
from PIL import Image
from io import BytesIO
import io
import base64
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image as ReportLabImage
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
from reportlab.pdfgen import canvas
from html2text import html2text
import PyPDF2
from fastapi import FastAPI, HTTPException, Depends, File, UploadFile, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel
from typing import Optional, List
import uvicorn
import asyncio
from fastapi import BackgroundTasks

# Load environment variables
load_dotenv()

# Configure Google Generative AI
genai.configure(api_key=google_api_key)

# FastAPI app initialization
app = FastAPI(
    title="MedCode AI API",
    description="API for MedCode AI medical assistant",
    version="1.0.0"
)

# Allow CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# PostgreSQL Database Configuration
def get_db_connection():
    """
    Create a secure PostgreSQL connection using environment variables from .env
    """
    try:
        load_dotenv()
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            raise Exception("DATABASE_URL not found in environment variables")
        if "render.com" in database_url:
            database_url += "?sslmode=require"
        conn = psycopg2.connect(database_url)
        return conn
    except Exception as e:
        raise Exception(f"Database connection error: {str(e)}")

# Pydantic models for request/response
class UserLogin(BaseModel):
    username: str
    password: str

class UserSignup(BaseModel):
    username: str
    password: str
    name: str
    email: str
    role: str

class QueryRequest(BaseModel):
    user_id: int
    query: str
    role: str
    has_image: Optional[bool] = False

class QueryHistoryResponse(BaseModel):
    id: int
    query: str
    role: str
    timestamp: str
    has_image: bool

# Example endpoint: User login
@app.post("/login")
def api_login(user: UserLogin):
    authenticated, role, name, user_id = verify_credentials(user.username, user.password)
    if not authenticated:
        raise HTTPException(status_code=401, detail="Invalid credentials")
    return {"authenticated": True, "role": role, "name": name, "user_id": user_id}

# Example endpoint: User signup
@app.post("/signup")
def api_signup(user: UserSignup):
    success, message = create_account(user.username, user.password, user.name, user.email, user.role)
    if not success:
        raise HTTPException(status_code=400, detail=message)
    return {"success": True, "message": message}

# Example endpoint: Query medical analysis (text only)
@app.post("/query")
def api_query(req: QueryRequest):
    # You can expand this to handle images/files via multipart/form-data
    # For now, only text queries are supported
    prompt_parts = [req.query, role_prompts.get(req.role, "")]
    response = model.generate_content(prompt_parts)
    if not response:
        raise HTTPException(status_code=500, detail="AI model failed to generate response")
    save_query_history(req.user_id, req.query, req.role, req.has_image)
    return {"response": response.text}

# Example endpoint: Get user query history
@app.get("/history/{user_id}", response_model=List[QueryHistoryResponse])
def api_history(user_id: int, limit: int = 20):
    history = get_user_query_history(user_id, limit)
    return history
# You can add more endpoints for profile, password change, etc.
# To run: uvicorn app:app --reload
# Initialize database and create tables if they don't exist
def init_db():
    """
    Initialize the database and create necessary tables if they don't exist
    """
    conn = get_db_connection()
    if conn:
        try:
            # Create a cursor
            cur = conn.cursor()
            
            # Create users table if it doesn't exist
            cur.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(256) NOT NULL,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                role VARCHAR(20) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # Create query_history table
            cur.execute('''
            CREATE TABLE IF NOT EXISTS query_history (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id),
                query TEXT NOT NULL,
                role VARCHAR(20) NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                has_image BOOLEAN DEFAULT FALSE
            )
            ''')
            
            # Add sample users if the table is empty
            cur.execute("SELECT COUNT(*) FROM users")
            if cur.fetchone()[0] == 0:
                sample_users = [
                    ('doctor1', hashlib.sha256('doctor123'.encode()).hexdigest(), 'Dr. Smith', '<EMAIL>', 'Doctor'),
                    ('student1', hashlib.sha256('student123'.encode()).hexdigest(), 'Jane Student', '<EMAIL>', 'Student'),
                    ('user1', hashlib.sha256('user123'.encode()).hexdigest(), 'John Doe', '<EMAIL>', 'Common User')
                ]
                cur.executemany("INSERT INTO users (username, password, name, email, role) VALUES (%s, %s, %s, %s, %s)", 
                               sample_users)
            
            # Commit changes
            conn.commit()
            
            # Close cursor
            cur.close()
            
            return conn
        except psycopg2.Error as e:
            print(f"Database initialization error: {e}")
            if conn:
                conn.close()
            return None
    return None

# Authentication Functions
def verify_credentials(username, password):
    """
    Verify user credentials against the database
    
    Args:
        username (str): The username to verify
        password (str): The password to verify
        
    Returns:
        tuple: (authenticated, role, name, user_id) where authenticated is a boolean
    """
    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("SELECT id, password, role, name FROM users WHERE username=%s", (username,))
                result = cur.fetchone()
                
                if result:
                    stored_password = result['password']
                    role = result['role']
                    name = result['name']
                    user_id = result['id']
                    
                    if stored_password == hashlib.sha256(password.encode()).hexdigest():
                        conn.close()
                        return True, role, name, user_id
            conn.close()
        except psycopg2.Error as e:
            print(f"Authentication error: {e}")
            if conn:
                conn.close()
    
    return False, None, None, None

def create_account(username, password, name, email, role):
    """
    Create a new user account in the database
    
    Args:
        username (str): Username for the new account
        password (str): Password for the new account
        name (str): Full name of the user
        email (str): Email address
        role (str): User role (Student, Doctor, Common User)
        
    Returns:
        tuple: (success, message) where success is a boolean
    """
    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor() as cur:
                # Check if username already exists
                cur.execute("SELECT COUNT(*) FROM users WHERE username=%s", (username,))
                if cur.fetchone()[0] > 0:
                    conn.close()
                    return False, "Username already exists"
                
                # Check if email already exists
                cur.execute("SELECT COUNT(*) FROM users WHERE email=%s", (email,))
                if cur.fetchone()[0] > 0:
                    conn.close()
                    return False, "Email already exists"
                
                # Create new user
                hashed_pw = hashlib.sha256(password.encode()).hexdigest()
                cur.execute("INSERT INTO users (username, password, name, email, role) VALUES (%s, %s, %s, %s, %s)",
                          (username, hashed_pw, name, email, role))
                conn.commit()
                conn.close()
                return True, "Account created successfully!"
        except psycopg2.Error as e:
            if conn:
                conn.close()
            return False, f"Error creating account: {str(e)}"
    
    return False, "Database connection error"

def update_profile(user_id, name=None, email=None):
    """
    Update user profile information
    
    Args:
        user_id (int): User ID to update
        name (str, optional): New name
        email (str, optional): New email
        
    Returns:
        tuple: (success, message) where success is a boolean
    """
    if not name and not email:
        return False, "No updates provided"
    
    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor() as cur:
                if name and email:
                    cur.execute("UPDATE users SET name=%s, email=%s WHERE id=%s", 
                              (name, email, user_id))
                elif name:
                    cur.execute("UPDATE users SET name=%s WHERE id=%s", 
                              (name, user_id))
                elif email:
                    # Check if email already exists
                    cur.execute("SELECT COUNT(*) FROM users WHERE email=%s AND id!=%s", 
                              (email, user_id))
                    if cur.fetchone()[0] > 0:
                        conn.close()
                        return False, "Email already exists"
                    
                    cur.execute("UPDATE users SET email=%s WHERE id=%s", 
                              (email, user_id))
                
                conn.commit()
                conn.close()
                return True, "Profile updated successfully!"
        except psycopg2.Error as e:
            if conn:
                conn.close()
            return False, f"Error updating profile: {str(e)}"
    
    return False, "Database connection error"

def change_password(user_id, current_password, new_password):
    """
    Change user's password
    
    Args:
        user_id (int): User ID
        current_password (str): Current password
        new_password (str): New password
        
    Returns:
        tuple: (success, message) where success is a boolean
    """
    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor() as cur:
                # Verify current password
                cur.execute("SELECT password FROM users WHERE id=%s", (user_id,))
                result = cur.fetchone()
                
                if result and result[0] == hashlib.sha256(current_password.encode()).hexdigest():
                    # Update password
                    hashed_new_pw = hashlib.sha256(new_password.encode()).hexdigest()
                    cur.execute("UPDATE users SET password=%s WHERE id=%s", 
                              (hashed_new_pw, user_id))
                    conn.commit()
                    conn.close()
                    return True, "Password changed successfully!"
                else:
                    conn.close()
                    return False, "Current password is incorrect"
        except psycopg2.Error as e:
            if conn:
                conn.close()
            return False, f"Error changing password: {str(e)}"
    
    return False, "Database connection error"

def save_query_history(user_id, query, role, has_image=False):
    """
    Save user query to history
    
    Args:
        user_id (int): User ID
        query (str): The user's query
        role (str): The role used for the query
        has_image (bool): Whether the query included an image
        
    Returns:
        bool: Success or failure
    """
    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor() as cur:
                cur.execute(
                    "INSERT INTO query_history (user_id, query, role, has_image) VALUES (%s, %s, %s, %s)",
                    (user_id, query, role, has_image)
                )
                conn.commit()
                conn.close()
                return True
        except psycopg2.Error as e:
            print(f"Error saving query history: {e}")
            if conn:
                conn.close()
    
    return False

def get_user_query_history(user_id, limit=20):
    """
    Retrieve query history for a specific user
    
    Args:
        user_id (int): User ID
        limit (int): Maximum number of records to return
        
    Returns:
        list: List of query history records
    """
    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute(
                    "SELECT id, query, role, timestamp, has_image FROM query_history " +
                    "WHERE user_id=%s ORDER BY timestamp DESC LIMIT %s",
                    (user_id, limit)
                )
                history = cur.fetchall()
                conn.close()
                return history
        except psycopg2.Error as e:
            print(f"Error retrieving query history: {e}")
            if conn:
                conn.close()
    
    return []

def get_user_stats(user_id):
    """
    Get user statistics
    
    Args:
        user_id (int): User ID
        
    Returns:
        dict: Dictionary containing user statistics
    """
    conn = get_db_connection()
    stats = {
        "total_queries": 0,
        "queries_today": 0,
        "days_active": 0
    }
    
    if conn:
        try:
            with conn.cursor() as cur:
                # Total queries
                cur.execute("SELECT COUNT(*) FROM query_history WHERE user_id=%s", (user_id,))
                stats["total_queries"] = cur.fetchone()[0]
                
                # Queries today
                cur.execute(
                    "SELECT COUNT(*) FROM query_history WHERE user_id=%s AND DATE(timestamp) = CURRENT_DATE",
                    (user_id,)
                )
                stats["queries_today"] = cur.fetchone()[0]
                
                # Days active
                cur.execute(
                    "SELECT COUNT(DISTINCT DATE(timestamp)) FROM query_history WHERE user_id=%s",
                    (user_id,)
                )
                stats["days_active"] = cur.fetchone()[0]
                
                conn.close()
                return stats
        except psycopg2.Error as e:
            print(f"Error retrieving user stats: {e}")
            if conn:
                conn.close()
    
    return stats

def update_user_preferences(user_id, default_role):
    """
    Update user preferences
    
    Args:
        user_id (int): User ID
        default_role (str): Default role preference
        
    Returns:
        tuple: (success, message) where success is a boolean
    """
    conn = get_db_connection()
    if conn:
        try:
            with conn.cursor() as cur:
                cur.execute("UPDATE users SET role=%s WHERE id=%s", 
                          (default_role, user_id))
                conn.commit()
                conn.close()
                return True, "Preferences updated successfully!"
        except psycopg2.Error as e:
            if conn:
                conn.close()
            return False, f"Error updating preferences: {str(e)}"
    
    return False, "Database connection error"

# Create a .env file template
def create_env_file_template():
    """
    Create a template .env file for database configuration
    """
    if not os.path.exists('.env'):
        with open('.env', 'w') as f:
            f.write('''# PostgreSQL Database Configuration
            DB_HOST=localhost
            DB_NAME=medcodeai
            DB_USER=postgres
            DB_PASSWORD=your_password
            DB_PORT=5432
        '''
        )
        return True
    return False

# Model Configuration
generation_config = {
    "temperature": 0.2,
    "top_p": 0.9,
    "top_k": 40,
    "max_output_tokens": 8192,
}

safety_settings = [
    {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
]

# Initialize model
model = genai.GenerativeModel(model_name="gemini-2.0-flash",
                             generation_config=generation_config,
                             safety_settings=safety_settings)

# Function to Fetch Research Papers from PubMed
def fetch_medical_articles_sync(query, num_articles=5):
    url = f"https://pubmed.ncbi.nlm.nih.gov/?term={query.replace(' ', '+')}"
    headers = {"User-Agent": "Mozilla/5.0"}
    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.text, "html.parser")

    articles = []
    for article in soup.find_all("article", class_="full-docsum")[:num_articles]:
        title = article.find("a", class_="docsum-title").text.strip()
        link = "https://pubmed.ncbi.nlm.nih.gov" + article.find("a", class_="docsum-title")["href"]
        articles.append(f"🔹 [{title}]({link})")
    return "\n".join(articles) if articles else "No relevant research papers found."

async def fetch_medical_articles_async(query, num_articles=5):
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, fetch_medical_articles_sync, query, num_articles)

def fetch_medical_articles(query, num_articles=5):
    # Use asyncio for FastAPI context
    try:
        return asyncio.run(fetch_medical_articles_async(query, num_articles))
    except RuntimeError:
        # If already in an event loop, fallback to sync
        return fetch_medical_articles_sync(query, num_articles)

# FastAPI endpoint for research papers (for frontend async calls)
class PubMedRequest(BaseModel):
    query: str
    num_articles: Optional[int] = 5

@app.post("/pubmed")
async def api_pubmed(req: PubMedRequest):
    try:
        articles = await fetch_medical_articles_async(req.query, req.num_articles)
        return {"results": articles}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Role-Specific Prompts
role_prompts = {
    "Student": """
    You are a highly skilled AI assistant designed to help medical students.
    Your task is to provide in-depth explanations on medical conditions, treatment procedures, 
    and related case studies. Also, retrieve the latest research papers and medical findings.
    Provide links to research papers when applicable.
    
    Please structure the response as follows:
    1. *Detailed Explanation*  
    2. *Case Studies*  
    3. *Research Paper References* (Fetched from PubMed)
    """,
    
    "Doctor": """
    You are an AI assistant Expert for medical professionals. Your job is to analyze patient data, 
    highlight critical details, and suggest possible best treatment methodologies used worldwide, and also
    make sure you are an expert in advising the doctor.
    Ensure the response includes:
    
    1. *Detailed Medical Analysis*  
    2. *Key Observations*  
    3. *Potential Treatment Approaches*
    """,
    
    "Common User": """
    You are a friendly AI assistant that helps normal users understand medical conditions.
    Your job is to provide simple, easy-to-understand explanations of medical diagnoses, it should
    be easily understandable by users in simple way.
    
    Structure the response as follows:
    1. *What the condition means in simple words*  
    2. *Common Symptoms*  
    3. *What steps to take next*
    """
}

# Function to display role-specific icons and colors (for API responses)
def get_role_info(role):
    if role == "Student":
        return "🎓", "#1E88E5", "Explore medical knowledge through research and case studies"
    elif role == "Doctor":
        return "👨‍⚕️", "#43A047", "Analyze patient data and treatment methodologies"
    elif role == "Common User":
        return "🧑‍💼", "#FFA000", "Understand medical conditions in simple terms"
    return "", "#607D8B", ""

# Additional FastAPI endpoints for profile management
def create_medical_pdf(user_name, role, query, response_text, image_data=None, pdf_content=None, language="English"):
    """
    Create a PDF document containing the medical query and response
    
    Args:
        user_name (str): Name of the user
        role (str): Selected role (Student, Doctor, Common User)
        query (str): The user's query
        response_text (str): The AI response text
        image_data (bytes, optional): Image data if an image was uploaded
        pdf_content (str, optional): Content extracted from uploaded PDF
        language (str, optional): Language of the response
        
    Returns:
        bytes: PDF file as bytes
    """
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=letter, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)
    
    # Create the styles
    styles = getSampleStyleSheet()
    
    # Define custom styles without overriding existing ones
    title_style = ParagraphStyle(
        name='CustomTitle', 
        parent=styles['Title'],
        fontSize=16, 
        alignment=TA_CENTER,
        spaceAfter=12
    )
    
    heading_style = ParagraphStyle(
        name='CustomHeading', 
        parent=styles['Heading1'],
        fontSize=14,
        spaceAfter=8
    )
    
    normal_style = ParagraphStyle(
        name='CustomNormal', 
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=6
    )
    
    subheading_style = ParagraphStyle(
        name='CustomSubheading', 
        parent=styles['Heading2'],
        fontSize=12,
        spaceAfter=6
    )
    
    # Create the content
    content = []
    
    # Title
    content.append(Paragraph("MedCode AI - Medical Analysis", title_style))
    
    # Current date and time
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    content.append(Paragraph(f"Generated on: {now}", normal_style))
    content.append(Spacer(1, 0.2*inch))
    
    # User info
    content.append(Paragraph(f"User: {user_name}", normal_style))
    content.append(Paragraph(f"Role: {role}", normal_style))
    
    # Add language information if not English
    if language != "English":
        content.append(Paragraph(f"Language: {language}", normal_style))
    
    content.append(Spacer(1, 0.2*inch))
    
    # Query section
    content.append(Paragraph("Query:", heading_style))
    content.append(Paragraph(query, normal_style))
    content.append(Spacer(1, 0.3*inch))
    
    # Add image if provided
    if image_data:
        try:
            img_buffer = io.BytesIO(image_data)
            img = ReportLabImage(img_buffer, width=4*inch, height=3*inch)
            content.append(Paragraph("Uploaded Image:", subheading_style))
            content.append(img)
            content.append(Spacer(1, 0.3*inch))
        except Exception as e:
            content.append(Paragraph(f"Error displaying image: {str(e)}", normal_style))
    
    # Add PDF content if provided
    if pdf_content:
        content.append(Paragraph("Uploaded PDF Content:", subheading_style))
        # Limit PDF content to avoid very large PDFs
        pdf_preview = pdf_content[:1000] + "..." if len(pdf_content) > 1000 else pdf_content
        content.append(Paragraph(pdf_preview, normal_style))
        content.append(Spacer(1, 0.3*inch))
    
    # Response section
    content.append(Paragraph("Medical Analysis:", heading_style))
    
    # Convert markdown/HTML to plain text for PDF
    plain_text = html2text(response_text)
    paragraphs = plain_text.split('\n\n')
    
    for para in paragraphs:
        if para.strip():
            content.append(Paragraph(para.replace('\n', '<br/>'), normal_style))
    
    # Build the PDF
    doc.build(content)
    
    # Get the PDF as bytes
    pdf_bytes = buffer.getvalue()
    buffer.close()
    
    return pdf_bytes

def get_pdf_download_link(pdf_bytes, filename="medical_analysis.pdf"):
    """
    Generate a download link for the PDF
    
    Args:
        pdf_bytes (bytes): The PDF file as bytes
        filename (str): Name of the file to download
        
    Returns:
        str: HTML string with download link
    """
    b64 = base64.b64encode(pdf_bytes).decode()
    href = f'<a href="data:application/pdf;base64,{b64}" download="{filename}" ' \
           f'style="text-decoration:none; color:white; padding:10px 25px; ' \
           f'background-color:#0062E6; border-radius:30px; ' \
           f'box-shadow:0 8px 15px rgba(0,0,0,0.1); ' \
           f'display:inline-block; font-weight:bold; ' \
           f'transition:all 0.3s; text-transform:uppercase; ' \
           f'letter-spacing:1px;">' \
           f'📥 Download PDF</a>'
    return href

# Add this function to extract text from PDF files
def extract_text_from_pdf(pdf_file):
    """
    Extract text from a PDF file
    
    Args:
        pdf_file: The uploaded PDF file
        
    Returns:
        str: Extracted text from the PDF
    """
    pdf_text = ""
    try:
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(pdf_file.getvalue()))
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            pdf_text += page.extract_text() + "\n\n"
        return pdf_text
    except Exception as e:
        return f"Error extracting text from PDF: {str(e)}"

# Additional FastAPI endpoints for profile management
@app.post("/profile/update")
def api_update_profile(user_id: int, name: Optional[str] = None, email: Optional[str] = None):
    success, message = update_profile(user_id, name, email)
    if not success:
        raise HTTPException(status_code=400, detail=message)
    return {"success": True, "message": message}

@app.post("/profile/change-password")
def api_change_password(user_id: int, current_password: str, new_password: str):
    success, message = change_password(user_id, current_password, new_password)
    if not success:
        raise HTTPException(status_code=400, detail=message)
    return {"success": True, "message": message}

@app.get("/stats/{user_id}")
def api_user_stats(user_id: int):
    stats = get_user_stats(user_id)
    return stats

# FastAPI startup event
@app.on_event("startup")
async def startup_event():
    """Initialize database on startup"""
    init_db()

# Main function to run the FastAPI server
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
            
            # Display current role
            current_role_icon, current_role_color, _ = get_role_info(st.session_state.selected_role)
            st.markdown(f"<h3>Current Role: {current_role_icon} {st.session_state.selected_role}</h3>", unsafe_allow_html=True)
            
            # Query input section with language selection
            st.markdown('<div class="query-container">', unsafe_allow_html=True)
            st.subheader("Enter Your Medical Query")

            # Voice input option
            voice_text = get_voice_input()

            # Text area for query input
            user_query = st.text_area(
                label="", 
                value=voice_text if voice_text else "",
                placeholder="Enter your medical query here... (e.g., 'What are the latest treatments for type 2 diabetes?')", 
                height=120,
                key="query_text_area"
            )

            # File uploader for images and PDFs
            st.write("Upload a relevant medical image or PDF document (optional):")
            file_uploaded = st.file_uploader("Upload file", type=["jpg", "jpeg", "png", "pdf"], label_visibility="collapsed")

            if file_uploaded:
                # Create a thumbnail with shadow effect
                st.markdown("""
                <style>
                .image-container {
                    padding: 10px;
                    background: white;
                    border-radius: 10px;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                    display: inline-block;
                    margin-top: 10px;
                }
                </style>
                """, unsafe_allow_html=True)
                
                st.markdown('<div class="image-container">', unsafe_allow_html=True)
                
                # Handle different file types
                if file_uploaded.type.startswith('image'):
                    st.image(file_uploaded, width=200, caption="Uploaded Image", use_container_width=False)
                elif file_uploaded.type == 'application/pdf':
                    st.write(f"📄 PDF Uploaded: {file_uploaded.name}")
                    # Preview option for PDF
                    if st.button("Preview PDF Content"):
                        pdf_text = extract_text_from_pdf(file_uploaded)
                        st.text_area("PDF Content Preview (first 500 chars):", value=pdf_text[:500] + "...", height=150)
                
                st.markdown('</div>', unsafe_allow_html=True)

            # Language selection before generating output
            st.subheader("Select Output Language")
            languages = [
                "English", "Spanish", "French", "German", "Chinese", 
                "Japanese", "Russian", "Arabic", "Hindi", "Portuguese", 
                "Italian", "Dutch", "Korean", "Swedish", "Turkish", "Kannada"
            ]

            # Initialize session state for output language
            if 'output_language' not in st.session_state:
                st.session_state.output_language = "English"

            # Language selector
            output_language = st.selectbox(
                "Choose the language for your medical analysis:", 
                languages, 
                index=0,
                key="output_language"
            )

            st.markdown('</div>', unsafe_allow_html=True)

            # Submit button with enhanced styling
            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                submit = st.button("🔍 Generate Medical Analysis", use_container_width=True)
            
            st.markdown('</div>', unsafe_allow_html=True)
            
            # Results section with language handling
            if submit:
                if not user_query:
                    st.error("Please enter a medical query before submitting.")
                else:
                    # Add to query history
                    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                    st.session_state.query_history.append({
                        "query": user_query,
                        "role": st.session_state.selected_role,
                        "timestamp": timestamp,
                        "has_attachment": file_uploaded is not None,
                        "has_image": file_uploaded is not None and file_uploaded.type.startswith('image'),
                        "language": output_language
                    })
                    
                    # Save query to database
                    save_query_history(
                        st.session_state.user_id,
                        user_query,
                        st.session_state.selected_role,
                        file_uploaded is not None and file_uploaded.type.startswith('image')
                    )
                    
                    st.markdown('<div class="results-container">', unsafe_allow_html=True)
                    st.subheader(f"Medical Analysis for {st.session_state.selected_role}")
                    
                    if output_language != "English":
                        st.info(f"Generating response in {output_language}")
                    
                    # Display loading animation
                    with st.spinner(""):
                        st.markdown("""
                        <div class="loading-container">
                            <div class="loading-spinner"></div>
                        </div>
                        <p style="text-align: center; color: #666;">Generating your personalized medical information...</p>
                        """, unsafe_allow_html=True)
                        
                        prompt_parts = []
                        
                        # If file is uploaded, add to input
                        if file_uploaded:
                            if file_uploaded.type.startswith('image'):
                                image_data = file_uploaded.getvalue()
                                image_parts = [{"mime_type": file_uploaded.type, "data": image_data}]
                                prompt_parts.append(image_parts[0])
                            elif file_uploaded.type == 'application/pdf':
                                pdf_text = extract_text_from_pdf(file_uploaded)
                                prompt_parts.append(f"PDF CONTENT:\n{pdf_text}\n\nUSER QUERY:")
                        
                        # Add user query and role-based prompt
                        prompt_parts.append(user_query)
                        
                        # Add language instruction if not English
                        role_prompt = role_prompts[st.session_state.selected_role]
                        if output_language != "English":
                            role_prompt += f"\n\nPlease provide your response in {output_language}."
                        
                        prompt_parts.append(role_prompt)
                        
                        # Generate response
                        try:
                            response = model.generate_content(prompt_parts)
                            
                            if response:
                                # Add a small delay for visual effect
                                time.sleep(0.8)
                                
                                # Display formatted response
                                response_container = st.container()
                                with response_container:
                                    st.markdown('<div class="response-section">', unsafe_allow_html=True)
                                    st.markdown(response.text)
                                    st.markdown('</div>', unsafe_allow_html=True)
                                
                                # Create PDF
                                pdf_content = None
                                image_data = None

                                if file_uploaded:
                                    if file_uploaded.type.startswith('image'):
                                        image_data = file_uploaded.getvalue()
                                    elif file_uploaded.type == 'application/pdf':
                                        pdf_content = extract_text_from_pdf(file_uploaded)

                                pdf_bytes = create_medical_pdf(
                                    st.session_state.user_name,
                                    st.session_state.selected_role,
                                    user_query,
                                    response.text,
                                    image_data,
                                    pdf_content,
                                    output_language
                                )
                                
                                # Auto-download PDF and show download button
                                filename = f"MedCode_Analysis_{time.strftime('%Y%m%d_%H%M%S')}.pdf"

                                # Create download button that triggers automatic download
                                st.markdown("<div style='text-align: center; margin-top: 20px;'>", unsafe_allow_html=True)
                                st.download_button(
                                    label="📥 Download PDF Report",
                                    data=pdf_bytes,
                                    file_name=filename,
                                    mime="application/pdf",
                                    use_container_width=False
                                )
                                st.markdown("</div>", unsafe_allow_html=True)

                                # Also show success message
                                st.success("✅ PDF Report Generated Successfully! Click the button above to download.")
                                
                                # If Student → Fetch and display research papers
                                if st.session_state.selected_role == "Student":
                                    st.markdown('<div class="papers-container">', unsafe_allow_html=True)
                                    st.subheader("📚 Related Research Papers")
                                    pubmed_results = fetch_medical_articles(user_query)
                                    st.markdown(pubmed_results)
                                    st.markdown('</div>', unsafe_allow_html=True)
                        except Exception as e:
                            st.error(f"Error generating response: {str(e)}")
                    
                    st.markdown('</div>', unsafe_allow_html=True)
        
        elif page == "Query History":
            st.markdown('<div class="main-header"><h1>Your Query History</h1>'
                       '<p>Review your past medical queries and results</p></div>', 
                       unsafe_allow_html=True)
            
            if not st.session_state.query_history:
                st.info("You haven't made any queries yet. Go to the Dashboard to start!")
            else:
                # Sort history by timestamp (newest first)
                sorted_history = sorted(st.session_state.query_history, 
                                        key=lambda x: x["timestamp"], 
                                        reverse=True)
                
                for i, item in enumerate(sorted_history):
                    with st.expander(f"Query: {item['query'][:50]}{'...' if len(item['query']) > 50 else ''}", expanded=(i==0)):
                        st.markdown(f"""
                        <div class="history-card">
                            <p><strong>Query:</strong> {item['query']}</p>
                            <p><strong>Role:</strong> {item['role']}</p>
                            <p class="history-timestamp">Time: {item['timestamp']}</p>
                            {'<p>📎 Attachment was included</p>' if item.get('has_attachment', False) else ''}
                        </div>
                        """, unsafe_allow_html=True)
                        
                        if st.button(f"Rerun this query", key=f"rerun_{i}"):
                            st.session_state.selected_role = item['role']
                            st.session_state.rerun_query = item['query']
                            # Switch to dashboard
                            st.rerun()
        
        elif page == "Settings":
            st.markdown('<div class="main-header"><h1>Account Settings</h1>'
                       '<p>Manage your profile and preferences</p></div>', 
                       unsafe_allow_html=True)
            
            # Create tabs for different settings
            tab1, tab2 = st.tabs(["Profile", "Preferences"])
            
            with tab1:
                st.subheader("Profile Information")
                
                # Fetch actual user email from the database
                def get_user_email(user_id):
                    conn = get_db_connection()
                    if conn:
                        try:
                            with conn.cursor() as cur:
                                cur.execute("SELECT email FROM users WHERE id=%s", (user_id,))
                                result = cur.fetchone()
                                conn.close()
                                if result:
                                    return result[0]
                        except Exception as e:
                            if conn:
                                conn.close()
                    return ""
                
                user_email = get_user_email(st.session_state.user_id)
                
                # Show current profile info
                st.markdown(f"""
                <div style="background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.05);">
                    <p><strong>Username:</strong> {st.session_state.username}</p>
                    <p><strong>Name:</strong> {st.session_state.user_name}</p>
                    <p><strong>Email:</strong> {user_email}</p>
                    <p><strong>Role:</strong> {st.session_state.user_role}</p>
                                </div>
                """, unsafe_allow_html=True)
                current_password = st.text_input("Current Password", type="password")
                new_password = st.text_input("New Password", type="password")
                confirm_password = st.text_input("Confirm New Password", type="password")

                col1, col2 = st.columns(2)
                with col1:
                    if st.button("Change Password"):
                        if current_password and new_password and confirm_password:
                            if new_password == confirm_password:
                                # Call change_password to update in DB
                                success, message = change_password(
                                    st.session_state.user_id,
                                    current_password,
                                    new_password
                                )
                                if success:
                                    st.success(message)
                                    time.sleep(1)
                                    st.rerun()
                                else:
                                    st.error(message)
                            else:
                                st.error("New passwords don't match")
                        else:
                            st.warning("Please fill all password fields")
                            # st.session_state.user_name = new_name  # Removed undefined variable usage
                            time.sleep(1)
                            st.rerun()
                
                # Security section
                st.subheader("Security")
                current_password = st.text_input("Current Password", type="password")
                new_password = st.text_input("New Password", type="password")
                confirm_password = st.text_input("Confirm New Password", type="password")
                
                col1, col2 = st.columns(2)
                with col1:
                    if st.button("Change Password"):
                        if current_password and new_password and confirm_password:
                            if new_password == confirm_password:
                                # Here you would validate the current password and update it
                                # We'll just show a success message
                                st.success("Password changed successfully!")
                            else:
                                st.error("New passwords don't match")
                        else:
                            st.warning("Please fill all password fields")
            
            with tab2:
                st.subheader("Application Preferences")
                
                # Theme preference
                theme = st.selectbox("Theme", ["Light", "Dark", "System Default"], index=0)
                
                # Notification settings
                st.subheader("Notifications")
                email_notifications = st.checkbox("Email Notifications", value=True)
                app_notifications = st.checkbox("Application Notifications", value=True)
                
                # Default role
                st.subheader("Default Role")
                default_role = st.selectbox("Set your default role when using the application", 
                                          ["Student", "Doctor", "Common User"],
                                          index=["Student", "Doctor", "Common User"].index(st.session_state.user_role))
                
                col1, col2 = st.columns(2)
                with col1:
                    if st.button("Save Preferences"):
                        st.session_state.user_role = default_role
                        st.success("Preferences saved successfully!")
                        time.sleep(0.2)
                        st.rerun()
    
    # Footer
    st.markdown('<div class="footer">© 2025 Evo-XAI| Privacy Policy | Terms of Service | Contact<br>'
               'Evo-XAI - Where AI Meets Healthcare, Smarter Insights, Better Outcomes!</div>', 
               unsafe_allow_html=True)

if __name__ == "__main__":
    main()
